import { createSlice } from "@reduxjs/toolkit";
import { createAsyncThunk } from "@reduxjs/toolkit";

export const Login = createAsyncThunk(
  "user/login",
  async (data: { email: string; password: string }) => {
    const response = await fetch(
      "https://linked-posts.routemisr.com/users/signin",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }
    );
    return response.json();
  }
);
export const GetUserData = createAsyncThunk(
  "user/GetUserData",
  async (token: string) => {
    const response = await fetch(
      "https://linked-posts.routemisr.com/users/profile-data",
      {
        method: "Get",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(token),
      }
    );
    return response.json();
  }
);

const userSlice = createSlice({
  name: "user",
  initialState: {
    value: 0,
    token: null,
    isLoading: false,
    isError: false,
    userData: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(Login.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(Login.fulfilled, (state, action) => {
      state.isLoading = false;
      state.token = action.payload.token;
    });
    builder.addCase(Login.rejected, (state) => {
      state.isLoading = false;
      state.isError = true;
    });
    builder.addCase(GetUserData.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(GetUserData.fulfilled, (state, action) => {
      state.isLoading = false;
      state.userData = action.payload.user;
    });
    builder.addCase(GetUserData.rejected, (state) => {
      state.isLoading = false;
      state.isError = true;
    });
  },
});

export default userSlice.reducer;

