{"name": "social-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fontsource/roboto": "^5.2.8", "@mui/icons-material": "^7.3.2", "@mui/material": "^7.3.2", "@mui/material-nextjs": "^7.3.2", "@mui/styled-engine-sc": "^7.3.2", "@reduxjs/toolkit": "^2.9.0", "flowbite": "^3.1.2", "flowbite-react": "^0.12.9", "formik": "^2.4.6", "next": "15.5.4", "react": "19.1.0", "react-dom": "19.1.0", "react-redux": "^9.2.0", "styled-components": "^6.1.19"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.4", "tailwindcss": "^4", "typescript": "^5"}}