import { Switch, Al<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/material";
import CheckIcon from "@mui/icons-material/Check";
import Fingerprint from "@mui/icons-material/Fingerprint";
import * as React from "react";

export default function Home() {
  const label = { inputProps: { "aria-label": "Switch demo" } };
  return (
    <>
      <div className="w-full bg-gray-700 min-h-screen flex text-3xl capitalize font-extrabold text-white">
        <div className="m-auto flex flex-col items-center gap-3">
          <h2>home</h2>
          <Switch {...label} defaultChecked />
          <Alert
            icon={<CheckIcon fontSize="inherit" />}
            variant="outlined"
            severity="success"
            sx={{
              border: "4px solid #111827",
              borderRadius: "100px",
              color: "white",
            }}
          >
            Here is a gentle confirmation that your action was successful.
          </Alert>
          <IconButton aria-label="fingerprint" color="secondary" size="large">
            <Fingerprint />
          </IconButton>
        </div>
      </div>
    </>
  );
}